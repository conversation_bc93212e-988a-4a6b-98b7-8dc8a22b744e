#!/bin/bash

# 重新发送遗漏交易测试脚本

set -e

echo "=== Resend Tool Test Script ==="

# 检查参数
if [ $# -lt 2 ]; then
    echo "Usage: $0 <from_height> <to_height> [config_file] [batch_size]"
    echo "Example: $0 73701092 73701200 config/config-prd.yaml 30"
    exit 1
fi

FROM_HEIGHT=$1
TO_HEIGHT=$2
CONFIG_FILE=${3:-"config/config.yaml"}
BATCH_SIZE=${4:-50}

echo "Parameters:"
echo "  From Height: $FROM_HEIGHT"
echo "  To Height: $TO_HEIGHT"
echo "  Config File: $CONFIG_FILE"
echo "  Batch Size: $BATCH_SIZE"
echo ""

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file $CONFIG_FILE not found"
    exit 1
fi

# 检查环境变量
if [ -z "$ENV" ]; then
    echo "Warning: ENV environment variable not set, using 'local'"
    export ENV=local
fi

echo "Environment: $ENV"
echo ""

# 构建工具
echo "Building resend tool..."
go build -o bin/resend cmd/resend/main.go

if [ $? -ne 0 ]; then
    echo "Error: Failed to build resend tool"
    exit 1
fi

echo "Build successful!"
echo ""

# 运行工具
echo "Running resend tool..."
echo "Command: ./bin/resend -conf=$CONFIG_FILE -from=$FROM_HEIGHT -to=$TO_HEIGHT -batch=$BATCH_SIZE"
echo ""

./bin/resend -conf="$CONFIG_FILE" -from="$FROM_HEIGHT" -to="$TO_HEIGHT" -batch="$BATCH_SIZE"

if [ $? -eq 0 ]; then
    echo ""
    echo "=== Resend tool completed successfully! ==="
else
    echo ""
    echo "=== Resend tool failed! ==="
    exit 1
fi
