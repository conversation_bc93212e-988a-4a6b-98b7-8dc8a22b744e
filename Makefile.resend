# Makefile for Resend Tool

.PHONY: build-resend clean-resend test-resend help-resend

# 构建重新发送工具
build-resend:
	@echo "Building resend tool..."
	@mkdir -p bin
	@go build -o bin/resend cmd/resend/main.go
	@echo "Resend tool built successfully: bin/resend"

# 清理构建文件
clean-resend:
	@echo "Cleaning resend build files..."
	@rm -f bin/resend
	@echo "Clean completed"

# 测试重新发送工具 (需要提供参数)
# 使用方法: make test-resend FROM=73701092 TO=73701200 CONFIG=config/config-prd.yaml BATCH=30
test-resend: build-resend
	@if [ -z "$(FROM)" ] || [ -z "$(TO)" ]; then \
		echo "Error: FROM and TO parameters are required"; \
		echo "Usage: make test-resend FROM=73701092 TO=73701200 [CONFIG=config/config.yaml] [BATCH=50]"; \
		exit 1; \
	fi
	@echo "Testing resend tool with parameters:"
	@echo "  FROM: $(FROM)"
	@echo "  TO: $(TO)"
	@echo "  CONFIG: $(or $(CONFIG),config/config.yaml)"
	@echo "  BATCH: $(or $(BATCH),50)"
	@echo ""
	@./bin/resend -conf="$(or $(CONFIG),config/config.yaml)" -from="$(FROM)" -to="$(TO)" -batch="$(or $(BATCH),50)"

# 运行重新发送工具
# 使用方法: make run-resend FROM=73701092 TO=73701200 CONFIG=config/config-prd.yaml BATCH=30
run-resend: build-resend
	@if [ -z "$(FROM)" ] || [ -z "$(TO)" ]; then \
		echo "Error: FROM and TO parameters are required"; \
		echo "Usage: make run-resend FROM=73701092 TO=73701200 [CONFIG=config/config.yaml] [BATCH=50]"; \
		exit 1; \
	fi
	@echo "Running resend tool..."
	@./bin/resend -conf="$(or $(CONFIG),config/config.yaml)" -from="$(FROM)" -to="$(TO)" -batch="$(or $(BATCH),50)"

# 显示帮助信息
help-resend:
	@echo "Resend Tool Makefile Commands:"
	@echo ""
	@echo "  build-resend    - Build the resend tool"
	@echo "  clean-resend    - Clean build files"
	@echo "  test-resend     - Test the resend tool (requires FROM and TO parameters)"
	@echo "  run-resend      - Run the resend tool (requires FROM and TO parameters)"
	@echo "  help-resend     - Show this help message"
	@echo ""
	@echo "Parameters:"
	@echo "  FROM            - Start block height (required)"
	@echo "  TO              - End block height (required)"
	@echo "  CONFIG          - Config file path (default: config/config.yaml)"
	@echo "  BATCH           - Batch size (default: 50)"
	@echo ""
	@echo "Examples:"
	@echo "  make build-resend"
	@echo "  make run-resend FROM=73701092 TO=73701200"
	@echo "  make run-resend FROM=73701092 TO=73701200 CONFIG=config/config-prd.yaml BATCH=30"
	@echo ""
	@echo "Environment Variables:"
	@echo "  ENV             - Environment (local/dev/test/prd)"
