# Resend Tool - 重新发送遗漏交易工具

## 功能说明

这个工具用于处理因区块遗漏而未给用户转账的交易。它会：

1. 扫描指定区块范围内的 Polygon 链上的 RewardWithdraw 事件
2. 检查数据库中是否已存在该交易的记录
3. 对于未处理的交易，计算汇率并执行 JU 链上的批量转账
4. 将记录保存到数据库和 Lark 多维表格

## 使用方法

### 基本用法

```bash
# 处理区块范围 73701092 到 73701200 的遗漏交易
go run cmd/resend/main.go -from=73701092 -to=73701200

# 指定配置文件和批量大小
go run cmd/resend/main.go -conf=config/config-prd.yaml -from=73701092 -to=73701200 -batch=30
```

### 参数说明

- `-conf`: 配置文件路径 (默认: "config/config.yaml")
- `-from`: 开始区块高度 (必需)
- `-to`: 结束区块高度 (必需)
- `-batch`: 批量处理大小 (默认: 50)

### 环境变量

- `ENV`: 环境标识 (local/dev/test/prd)，默认为 "local"

## 工作流程

1. **初始化**: 连接到 Polygon 和 JU 链，初始化数据库和 Lark 客户端
2. **区块扫描**: 按 100 个区块为单位扫描指定范围
3. **事件过滤**: 查找 RewardWithdraw 事件
4. **重复检查**: 检查数据库中是否已存在该 PolHash
5. **汇率计算**: 获取实时汇率并计算转账金额
6. **批量转账**: 按指定批量大小执行转账
7. **记录保存**: 保存到数据库和 Lark

## 安全特性

- **重复检查**: 防止重复处理同一笔交易
- **批量处理**: 减少 Gas 费用和网络负载
- **错误处理**: 详细的错误日志和恢复机制
- **交易确认**: 等待交易确认后再继续处理

## 注意事项

1. 确保钱包地址有足够的 JU 余额进行转账
2. 建议在测试环境先验证功能
3. 大范围区块扫描可能需要较长时间
4. 网络异常时工具会自动重试

## 示例输出

```
2024-01-01T10:00:00.000Z INFO resend tool started {"env": "prd"}
2024-01-01T10:00:01.000Z INFO starting resend process {"from": 73701092, "to": 73701200, "batch_size": 50}
2024-01-01T10:00:02.000Z INFO scanning blocks {"from": 73701092, "to": 73701191}
2024-01-01T10:00:05.000Z INFO found missed transaction {"txHash": "0x123...", "from": "0xabc...", "amount": "100.5"}
2024-01-01T10:00:10.000Z INFO batch transfer completed {"txHash": "https://explorer.juscan.io/tx/0x456...", "count": 3}
2024-01-01T10:00:15.000Z INFO resend process completed successfully
```
