package block

import (
	"bytes"
	"context"
	"crypto/ecdsa"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strconv"
	"sync/atomic"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	common2 "gitlab.jcwork.net/assets-management/sipanzi/common"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/contracts/batchtransfer"
	myprogram "gitlab.jcwork.net/assets-management/sipanzi/contracts/program"
	"gitlab.jcwork.net/assets-management/sipanzi/db"
	"gitlab.jcwork.net/assets-management/sipanzi/lark"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
)

type Watcher struct {
	ctx context.Context

	pCtx *parseContext
}

func NewWatcher(cfg config.Config) *Watcher {
	ctx := context.Background()

	polCli, err := ethclient.Dial(cfg.Chain.PolygonRpcUrl)
	if err != nil {
		panic(err)
	}

	juCli, err := ethclient.Dial(cfg.Chain.JuRpcUrl)
	if err != nil {
		panic(err)
	}

	larkCli := lark.NewLark(cfg.Lark)

	batchTransfer, _ := batchtransfer.NewBatchTransfer(common.HexToAddress(consts.TransferBatchContract), juCli)

	mpg, _ := myprogram.NewProgram(common.Address{}, polCli)

	pk := common2.GetPK(cfg.Chain.Seed)
	auth, err := bind.NewKeyedTransactorWithChainID(pk, big.NewInt(210000))
	if err != nil {
		panic(err)
	}

	pCtx := &parseContext{
		mpg:           mpg,
		ctx:           ctx,
		polCli:        polCli,
		juCli:         juCli,
		startBlock:    cfg.Chain.StartBlock,
		maxBlockStep:  uint64(cfg.Chain.MaxBlockStep),
		batchTransfer: batchTransfer,
		lark:          larkCli,
		pk:            pk,
		signerFn:      auth.Signer,
		address:       crypto.PubkeyToAddress(pk.PublicKey),
		webhook:       cfg.Lark.Webhook,
		batchCount:    cfg.Lark.BatchCount,
	}

	return &Watcher{
		ctx:  ctx,
		pCtx: pCtx,
	}
}

func (w *Watcher) Start() {
	w.pCtx.start()
}

type parseContext struct {
	ctx    context.Context
	polCli *ethclient.Client
	juCli  *ethclient.Client

	tableId string

	lark *lark.Lark

	latestHeight         atomic.Uint64
	currentForwardHeight atomic.Uint64

	maxBlockStep uint64

	//programParser *program.Parser
	batchTransfer *batchtransfer.BatchTransfer

	startBlock uint64

	mpg *myprogram.Program

	pk       *ecdsa.PrivateKey
	signerFn bind.SignerFn
	address  common.Address

	webhook string

	batchCount int

	// 基于内存的区块跟踪器
	blockTracker *MemoryBlockTracker
}

func (w *parseContext) start() {
	log.Logger.Info("load block watcher")

	w.checkAndGetTableId()

	w.updateLatestHeight()

	// 启动时查询地址余额并发送Lark通知
	w.checkBalanceAndNotify("Ju 分发开始")

	height := w.startBlock

	w.currentForwardHeight.Store(height)

	go w.watchForward()
}

func (w *parseContext) updateLatestHeight() {
	go func() {
		for {
			height, err := w.polCli.BlockNumber(w.ctx)
			if err != nil {
				log.Logger.Error("get block number error", zap.Error(err))
				time.Sleep(time.Millisecond * 500)
				continue
			}

			w.latestHeight.Store(height)
			time.Sleep(time.Millisecond * 500)
		}
	}()
}

func (w *parseContext) watchForward() {

	for {
		latestHeight := w.latestHeight.Load()
		if latestHeight == 0 {
			time.Sleep(time.Millisecond * 100)
			continue
		}

		height := w.currentForwardHeight.Load()
		if height >= latestHeight {
			time.Sleep(time.Millisecond * 100)
			continue
		}

		toHeight := height + w.maxBlockStep
		if toHeight > latestHeight {
			toHeight = latestHeight
		}

		if height > toHeight {
			time.Sleep(time.Second)
			continue
		}

		w.parseByBlock(height, toHeight)

		w.currentForwardHeight.Store(toHeight + 1)
		log.Logger.Info("parsed forward", zap.Uint64("from height", height), zap.Uint64("to height", toHeight))
		time.Sleep(time.Millisecond * 100)
	}
}

func (w *parseContext) parseByBlock(from, to uint64) {

	//quote
	juAp, polBp, err := polToJuQuote()
	if err != nil {
		log.Logger.Error("parse quote error", zap.Error(err))
		panic(err)
	}
	rate := polBp / juAp * (1 - consts.ADDITION)

	logs, err := w.polCli.FilterLogs(w.ctx, ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(from)),
		ToBlock:   big.NewInt(int64(to)),
		Addresses: []common.Address{common.HexToAddress("******************************************")},
		Topics: [][]common.Hash{
			{
				common.HexToHash(consts.TopicRewardWithdraw),
			},
		},
	})

	if err != nil {
		log.Logger.Error("filter logs error", zap.Error(err), zap.Uint64("from height", from), zap.Uint64("to height", to))
		time.Sleep(time.Millisecond * 500)
		return
	}

	for _, l := range logs {
		if l.Topics[0].String() == consts.TopicRewardWithdraw {

			rewardWithdrawLog, _ := w.mpg.ParseRewardWithdraw(l)

			if rewardWithdrawLog.ToJUAmount.Cmp(big.NewInt(0)) == 0 {
				continue
			}

			polAmountD := decimal.NewFromBigInt(rewardWithdrawLog.ToJUAmount, 0-18)
			juAmount := polAmountD.Mul(decimal.NewFromFloat(rate))

			record := common2.Record{
				PolHash:          l.TxHash,
				From:             rewardWithdrawLog.From,
				PolAmount:        polAmountD,
				JuAp:             juAp,
				PolAp:            polBp,
				Addition:         consts.ADDITION,
				Rate:             rate,
				JuAmount:         juAmount,
				TransferJuAmount: juAmount,
			}

			records = append(records, record)
			log.Logger.Info("parse reward withdraw", zap.Any("record", record))

			if len(records) >= w.batchCount {
				log.Logger.Info("transfer batch", zap.Int("count", len(records)))

				txHash, err := w.transferBatch(records)
				if err != nil {
					w.checkBalanceAndNotify("Ju 分发结束")
					panic(err)
				}

				receipt := w.getReceipt(txHash)
				gasUsed := decimal.NewFromBigInt(new(big.Int).SetUint64(receipt.GasUsed), 0-18)
				gasPerTransfer := gasUsed.Div(decimal.NewFromInt(int64(len(records))))

				log.Logger.Info("transfer batch", zap.String("txHash", "https://explorer.juscan.io/tx/"+txHash.String()))

				w.BatchRecord(records, txHash.String(), gasPerTransfer)
				w.BatchSave(records, txHash.String(), gasPerTransfer)
				w.checkAndGetTableId()

				records = records[:0]
			}
		}
	}
}

type TickerBook struct {
	Biz     string        `json:"biz"`
	Code    int           `json:"code"`
	Msg     string        `json:"msg"`
	MsgInfo []interface{} `json:"msgInfo"`
	Data    []struct {
		S  string `json:"s"`
		T  int64  `json:"t"`
		Ap string `json:"ap"`
		Aq string `json:"aq"`
		Bp string `json:"bp"`
		Bq string `json:"bq"`
	} `json:"data"`
}

func polToJuQuote() (float64, float64, error) {
	resp, err := http.Get("https://api.jucoin.com/v1/spot/public/ticker/book?symbols=ju_usdt,pol_usdt")
	if err != nil {
		return 0, 0, err
	}

	var tickerBook TickerBook
	err = json.NewDecoder(resp.Body).Decode(&tickerBook)

	ju, err := strconv.ParseFloat(tickerBook.Data[0].Ap, 64)
	if err != nil {
		return 0, 0, err
	}

	poly, err := strconv.ParseFloat(tickerBook.Data[1].Bp, 64)
	if err != nil {
		return 0, 0, err
	}

	return ju, poly, nil
}

var records []common2.Record

func (w *parseContext) transferBatch(records []common2.Record) (common.Hash, error) {
	var addresses []common.Address
	totalAmount := big.NewInt(0)
	var amounts []*big.Int

	// 提取所有记录中的地址和金额
	for _, record := range records {
		amount := record.TransferJuAmount.Mul(decimal.NewFromInt(1e18)).BigInt()
		amounts = append(amounts, amount)
		addresses = append(addresses, record.From)
		totalAmount.Add(totalAmount, amount)
	}

	nonce, err := w.juCli.PendingNonceAt(w.ctx, w.address)
	if err != nil {
		panic(err)
	}

	// 调用批量转账合约
	tx, err := w.batchTransfer.BatchTransferJU(&bind.TransactOpts{
		From:   w.address,
		Nonce:  big.NewInt(int64(nonce)),
		Signer: w.signerFn,
		Value:  totalAmount,
	}, addresses, amounts)

	if err != nil {
		log.Logger.Error("batch transfer failed", zap.Error(err))
		return common.Hash{}, err
	}

	log.Logger.Info("batch transfer success",
		zap.String("txHash", tx.Hash().Hex()),
		zap.Int("count", len(addresses)),
		zap.String("totalAmount", totalAmount.String()))

	return tx.Hash(), nil
}

func (w *parseContext) Record(record *common2.Record, juHash string, gasUsed decimal.Decimal) {
	fields := map[string]interface{}{
		"Pol hash":   record.PolHash.String(),
		"充币地址":       record.From,
		"充币数量":       record.JuAmount.String(),
		"Juusdt bp":  decimal.NewFromFloat(record.JuAp).String(),
		"Polusdt ap": decimal.NewFromFloat(record.PolAp).String(),
		"汇率加点":       decimal.NewFromFloat(record.Addition).String(),
		"实际汇率":       decimal.NewFromFloat(record.Rate),
		"ju 数量":      record.JuAmount.String(),
		"转账数量":       record.TransferJuAmount.String(),
		"转账时间":       time.Now().UnixMilli(),
		"转账地址":       record.From.String(),
		"是否转账":       "是",
		"Gas":        gasUsed,
		"转账 hash":    juHash,
	}

	err := w.lark.WriteToBitable(w.tableId, fields)
	if err != nil {
		log.Logger.Error("write to bitable error", zap.Error(err))
	}
}

func (w *parseContext) BatchRecord(batchRecords []common2.Record, juHash string, gasUsed decimal.Decimal) {
	var data []map[string]interface{}

	for _, record := range batchRecords {
		fields := map[string]interface{}{
			"Pol hash":   record.PolHash.String(),
			"充币地址":       record.From,
			"充币数量":       record.PolAmount.String(),
			"Juusdt bp":  decimal.NewFromFloat(record.JuAp).String(),
			"Polusdt ap": decimal.NewFromFloat(record.PolAp).String(),
			"汇率加点":       decimal.NewFromFloat(record.Addition).String(),
			"实际汇率":       decimal.NewFromFloat(record.Rate),
			"ju 数量":      record.JuAmount.String(),
			"转账数量":       record.TransferJuAmount.String(),
			"转账时间":       time.Now().UnixMilli(),
			"转账地址":       record.From.String(),
			"是否转账":       "是",
			"Gas":        gasUsed,
			"转账 hash":    juHash,
		}
		data = append(data, fields)
	}

	err := w.lark.BatchWriteToBitable(w.tableId, data)
	if err != nil {
		log.Logger.Error("write to bitable error", zap.Error(err))
	}
}

func (w *parseContext) BatchSave(batchRecords []common2.Record, juHash string, gasUsed decimal.Decimal) {
	var data []*db.Record

	for _, record := range batchRecords {
		r := &db.Record{
			PolHash:          record.PolHash.Hex(),
			From:             record.From.Hex(),
			PolAmount:        record.PolAmount.String(),
			JuAp:             record.JuAp,
			PolAp:            record.PolAp,
			Addition:         record.Addition,
			Rate:             record.Rate,
			JuAmount:         record.JuAmount.String(),
			TransferJuAmount: record.TransferJuAmount.String(),
			ToAddress:        record.From.Hex(),
			TransferTime:     time.Now().Unix(),
			Gas:              gasUsed.String(),
			JuHash:           juHash,
		}
		data = append(data, r)
	}

	err := db.GetDb().Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&data).Error
	if err != nil {
		log.Logger.Error("save to mysql error", zap.Error(err))
	}
}

func (w *parseContext) getReceipt(txHash common.Hash) *types.Receipt {
	for {
		juReceipt, err := w.juCli.TransactionReceipt(context.Background(), txHash)
		if err != nil {
			log.Logger.Info("retry get juReceipt")
			time.Sleep(time.Second)
			continue
		}
		return juReceipt
	}
}

func (w *parseContext) checkBitableAndCreate(txHash common.Hash) *types.Receipt {
	for {
		juReceipt, err := w.juCli.TransactionReceipt(context.Background(), txHash)
		if err != nil {
			log.Logger.Info("retry get juReceipt")
			time.Sleep(time.Second)
			continue
		}
		return juReceipt
	}
}

func (w *parseContext) checkAndGetTableId() string {
	tableId, err := w.lark.LastTableId()
	if err != nil {
		log.Logger.Error("get table id error", zap.Error(err))
		panic(err)
	}
	count, err := w.lark.TableRecordCount(tableId)
	if count > 19500 {
		random, _ := uuid.NewRandom()
		tableName := fmt.Sprintf("%s", random.String()[:8])
		tableId, err := w.lark.CreateTable(tableName, "充币记录")
		if err != nil {
			panic(err)
		}
		w.tableId = tableId
		return tableId
	}

	w.tableId = tableId
	return tableId

}

// checkBalanceAndNotify 查询地址余额并发送Lark通知
func (w *parseContext) checkBalanceAndNotify(msg string) {
	log.Logger.Info("开始查询地址余额", zap.String("address", w.address.Hex()))

	// 查询JU链上的余额
	juBalance, err := w.getAddressBalance(w.juCli, w.address)
	if err != nil {
		log.Logger.Error("查询JU链余额失败",
			zap.String("address", w.address.Hex()),
			zap.Error(err))
	}

	// 发送余额通知
	err = w.sendBalanceNotification(juBalance, msg)
	if err != nil {
		log.Logger.Error("发送余额通知失败", zap.Error(err))
	} else {
		log.Logger.Info("余额通知发送成功")
	}
}

// getAddressBalance 查询指定地址在指定链上的余额
func (w *parseContext) getAddressBalance(client *ethclient.Client, address common.Address) (float64, error) {
	balance, err := client.BalanceAt(w.ctx, address, nil)
	if err != nil {
		return 0, fmt.Errorf("查询余额失败: %w", err)
	}

	// 将余额转换为以太单位（18位小数）
	balanceEth := new(big.Float).Quo(new(big.Float).SetInt(balance), big.NewFloat(1e18))
	balanceFloat, _ := balanceEth.Float64()

	return balanceFloat, nil
}

// sendBalanceNotification 发送余额通知到Lark
func (w *parseContext) sendBalanceNotification(juBalance float64, msg string) error {
	// 构建通知消息
	message := fmt.Sprintf("📊 %s\n\n"+
		"🏠 监控地址: %s\n\n"+
		"💰 JU链余额: %.6f\n"+
		"⏰ 查询时间: %s\n"+
		"🔗 JU链浏览器: https://explorer.juscan.io/address/%s",
		msg,
		w.address.Hex(),
		juBalance,
		time.Now().Format("2006-01-02 15:04:05"),
		w.address.Hex())

	// 使用Lark webhook发送通知
	return w.sendLarkWebhookMessage(message)
}

// sendLarkWebhookMessage 通过webhook发送Lark消息
func (w *parseContext) sendLarkWebhookMessage(message string) error {
	// 这里需要使用webhook方式发送消息
	// 由于当前的lark实例是用于多维表格的，我们需要添加webhook功能

	// 构建webhook消息
	webhookMessage := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": message,
		},
	}

	// 将消息转换为JSON
	jsonData, err := json.Marshal(webhookMessage)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求到webhook
	// 注意：这里需要配置webhook URL，可以从配置文件中读取

	req, err := http.NewRequest("POST", w.webhook, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("webhook请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return nil
}
