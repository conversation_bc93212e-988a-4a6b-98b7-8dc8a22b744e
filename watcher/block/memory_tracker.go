package block

import (
	"fmt"
	"sync"
	"time"

	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"go.uber.org/zap"
)

// BlockRange 表示区块范围
type BlockRange struct {
	FromBlock uint64
	ToBlock   uint64
}

// BlockStatus 区块处理状态
type BlockStatus int

const (
	StatusPending BlockStatus = iota
	StatusProcessing
	StatusCompleted
	StatusFailed
)

// BlockRangeInfo 区块范围信息
type BlockRangeInfo struct {
	Range       BlockRange
	Status      BlockStatus
	RetryCount  int
	LastError   string
	ProcessedAt time.Time
}

// MemoryBlockTracker 基于内存的区块跟踪器
type MemoryBlockTracker struct {
	mu                sync.RWMutex
	processedRanges   map[string]*BlockRangeInfo // key: "from-to"
	failedRanges      []*BlockRangeInfo
	lastProcessedBlock uint64
	maxRetryCount     int
}

// NewMemoryBlockTracker 创建新的内存区块跟踪器
func NewMemoryBlockTracker(maxRetryCount int) *MemoryBlockTracker {
	return &MemoryBlockTracker{
		processedRanges: make(map[string]*BlockRangeInfo),
		failedRanges:    make([]*BlockRangeInfo, 0),
		maxRetryCount:   maxRetryCount,
	}
}

// GetRangeKey 获取区块范围的键
func (mbt *MemoryBlockTracker) GetRangeKey(from, to uint64) string {
	return fmt.Sprintf("%d-%d", from, to)
}

// MarkRangeProcessing 标记区块范围为处理中
func (mbt *MemoryBlockTracker) MarkRangeProcessing(from, to uint64) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()

	key := mbt.GetRangeKey(from, to)
	info := &BlockRangeInfo{
		Range:       BlockRange{FromBlock: from, ToBlock: to},
		Status:      StatusProcessing,
		ProcessedAt: time.Now(),
	}
	mbt.processedRanges[key] = info

	log.Logger.Debug("marked range as processing",
		zap.Uint64("from", from),
		zap.Uint64("to", to))
}

// MarkRangeCompleted 标记区块范围为已完成
func (mbt *MemoryBlockTracker) MarkRangeCompleted(from, to uint64) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()

	key := mbt.GetRangeKey(from, to)
	if info, exists := mbt.processedRanges[key]; exists {
		info.Status = StatusCompleted
		info.ProcessedAt = time.Now()
		
		// 更新最后处理的区块
		if to > mbt.lastProcessedBlock {
			mbt.lastProcessedBlock = to
		}
	}

	log.Logger.Debug("marked range as completed",
		zap.Uint64("from", from),
		zap.Uint64("to", to))
}

// MarkRangeFailed 标记区块范围为失败
func (mbt *MemoryBlockTracker) MarkRangeFailed(from, to uint64, errorMsg string) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()

	key := mbt.GetRangeKey(from, to)
	info, exists := mbt.processedRanges[key]
	if !exists {
		info = &BlockRangeInfo{
			Range: BlockRange{FromBlock: from, ToBlock: to},
		}
		mbt.processedRanges[key] = info
	}

	info.Status = StatusFailed
	info.RetryCount++
	info.LastError = errorMsg
	info.ProcessedAt = time.Now()

	// 如果重试次数未超过限制，添加到失败列表
	if info.RetryCount <= mbt.maxRetryCount {
		// 检查是否已经在失败列表中
		found := false
		for _, failedInfo := range mbt.failedRanges {
			if failedInfo.Range.FromBlock == from && failedInfo.Range.ToBlock == to {
				found = true
				break
			}
		}
		if !found {
			mbt.failedRanges = append(mbt.failedRanges, info)
		}
	}

	log.Logger.Warn("marked range as failed",
		zap.Uint64("from", from),
		zap.Uint64("to", to),
		zap.String("error", errorMsg),
		zap.Int("retry_count", info.RetryCount))
}

// GetFailedRanges 获取需要重试的失败区块范围
func (mbt *MemoryBlockTracker) GetFailedRanges() []*BlockRangeInfo {
	mbt.mu.RLock()
	defer mbt.mu.RUnlock()

	var retryableRanges []*BlockRangeInfo
	for _, info := range mbt.failedRanges {
		if info.Status == StatusFailed && info.RetryCount <= mbt.maxRetryCount {
			retryableRanges = append(retryableRanges, info)
		}
	}

	return retryableRanges
}

// RemoveFromFailedList 从失败列表中移除区块范围
func (mbt *MemoryBlockTracker) RemoveFromFailedList(from, to uint64) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()

	for i, info := range mbt.failedRanges {
		if info.Range.FromBlock == from && info.Range.ToBlock == to {
			// 从切片中移除
			mbt.failedRanges = append(mbt.failedRanges[:i], mbt.failedRanges[i+1:]...)
			break
		}
	}
}

// GetMissingRanges 获取遗漏的区块范围
func (mbt *MemoryBlockTracker) GetMissingRanges(startBlock, endBlock uint64, blockStep uint64) []BlockRange {
	mbt.mu.RLock()
	defer mbt.mu.RUnlock()

	var missingRanges []BlockRange
	currentBlock := startBlock

	// 生成应该处理的所有区块范围
	expectedRanges := make([]BlockRange, 0)
	for from := startBlock; from <= endBlock; from += blockStep {
		to := from + blockStep - 1
		if to > endBlock {
			to = endBlock
		}
		expectedRanges = append(expectedRanges, BlockRange{FromBlock: from, ToBlock: to})
	}

	// 检查哪些范围没有被成功处理
	for _, expectedRange := range expectedRanges {
		key := mbt.GetRangeKey(expectedRange.FromBlock, expectedRange.ToBlock)
		info, exists := mbt.processedRanges[key]
		
		if !exists || info.Status != StatusCompleted {
			missingRanges = append(missingRanges, expectedRange)
		}
	}

	return missingRanges
}

// GetLastProcessedBlock 获取最后处理的区块
func (mbt *MemoryBlockTracker) GetLastProcessedBlock() uint64 {
	mbt.mu.RLock()
	defer mbt.mu.RUnlock()
	return mbt.lastProcessedBlock
}

// SetLastProcessedBlock 设置最后处理的区块
func (mbt *MemoryBlockTracker) SetLastProcessedBlock(block uint64) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()
	mbt.lastProcessedBlock = block
}

// GetStats 获取统计信息
func (mbt *MemoryBlockTracker) GetStats() map[string]interface{} {
	mbt.mu.RLock()
	defer mbt.mu.RUnlock()

	stats := make(map[string]interface{})
	
	completedCount := 0
	failedCount := 0
	processingCount := 0
	
	for _, info := range mbt.processedRanges {
		switch info.Status {
		case StatusCompleted:
			completedCount++
		case StatusFailed:
			failedCount++
		case StatusProcessing:
			processingCount++
		}
	}

	stats["total_ranges"] = len(mbt.processedRanges)
	stats["completed_ranges"] = completedCount
	stats["failed_ranges"] = failedCount
	stats["processing_ranges"] = processingCount
	stats["retryable_failed_ranges"] = len(mbt.failedRanges)
	stats["last_processed_block"] = mbt.lastProcessedBlock

	return stats
}

// CleanupOldRanges 清理旧的已完成区块范围（保留内存）
func (mbt *MemoryBlockTracker) CleanupOldRanges(keepDuration time.Duration) {
	mbt.mu.Lock()
	defer mbt.mu.Unlock()

	cutoffTime := time.Now().Add(-keepDuration)
	
	for key, info := range mbt.processedRanges {
		if info.Status == StatusCompleted && info.ProcessedAt.Before(cutoffTime) {
			delete(mbt.processedRanges, key)
		}
	}

	log.Logger.Info("cleaned up old completed ranges",
		zap.Time("cutoff_time", cutoffTime),
		zap.Int("remaining_ranges", len(mbt.processedRanges)))
}
